<template>
  <div class="test-container">
    <h1>ImageGrid 组件测试</h1>
    
    <div class="test-section">
      <h2>1张图片 - 单图显示</h2>
      <ImageGrid :images="images1" key-prefix="test1" />
    </div>
    
    <div class="test-section">
      <h2>2张图片 - 2x2网格 (1行2列)</h2>
      <ImageGrid :images="images2" key-prefix="test2" />
    </div>
    
    <div class="test-section">
      <h2>3张图片 - 2x2网格 (第一行2张，第二行1张)</h2>
      <ImageGrid :images="images3" key-prefix="test3" />
    </div>
    
    <div class="test-section">
      <h2>4张图片 - 2x2网格 (标准2x2)</h2>
      <ImageGrid :images="images4" key-prefix="test4" />
    </div>
    
    <div class="test-section">
      <h2>5张图片 - 每行3列 (第一行3张，第二行2张)</h2>
      <ImageGrid :images="images5" key-prefix="test5" />
    </div>
    
    <div class="test-section">
      <h2>6张图片 - 每行3列 (标准2x3)</h2>
      <ImageGrid :images="images6" key-prefix="test6" />
    </div>
    
    <div class="test-section">
      <h2>9张图片 - 每行3列 (标准3x3)</h2>
      <ImageGrid :images="images9" key-prefix="test9" />
    </div>

    <!-- 移动端特殊测试 -->
    <div class="mobile-test-info">
      <h2>📱 移动端布局说明</h2>
      <div class="info-grid">
        <div class="info-item">
          <h3>屏幕宽度 < 360px</h3>
          <p>5张及以上图片使用单列布局</p>
        </div>
        <div class="info-item">
          <h3>屏幕宽度 360-374px</h3>
          <p>使用2列布局，减少padding</p>
        </div>
        <div class="info-item">
          <h3>屏幕宽度 ≥ 375px</h3>
          <p>5张及以上图片可使用3列布局</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageGrid from '../components/ImageGrid.vue'

export default {
  name: 'ImageGridTest',
  components: {
    ImageGrid
  },
  data() {
    return {
      // 测试用的示例图片URL
      sampleImages: [
        'https://picsum.photos/400/400?random=1',
        'https://picsum.photos/400/400?random=2',
        'https://picsum.photos/400/400?random=3',
        'https://picsum.photos/400/400?random=4',
        'https://picsum.photos/400/400?random=5',
        'https://picsum.photos/400/400?random=6',
        'https://picsum.photos/400/400?random=7',
        'https://picsum.photos/400/400?random=8',
        'https://picsum.photos/400/400?random=9'
      ]
    }
  },
  computed: {
    images1() {
      return this.sampleImages.slice(0, 1)
    },
    images2() {
      return this.sampleImages.slice(0, 2)
    },
    images3() {
      return this.sampleImages.slice(0, 3)
    },
    images4() {
      return this.sampleImages.slice(0, 4)
    },
    images5() {
      return this.sampleImages.slice(0, 5)
    },
    images6() {
      return this.sampleImages.slice(0, 6)
    },
    images9() {
      return this.sampleImages.slice(0, 9)
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  
  h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
  }
}

.test-section {
  margin-bottom: 3rem;
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
  
  h2 {
    margin-bottom: 1rem;
    color: #555;
    font-size: 1.2rem;
  }
}

// 移动端测试信息样式
.mobile-test-info {
  margin-top: 3rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  border: 1px solid #e1bee7;

  h2 {
    text-align: center;
    color: #4a148c;
    margin-bottom: 1.5rem;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.info-item {
  background: white;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  h3 {
    color: #6a1b9a;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
  }

  p {
    color: #666;
    font-size: 0.85rem;
    margin: 0;
  }
}

@media (max-width: 768px) {
  .test-container {
    padding: 1rem;
  }

  .test-section {
    padding: 1rem;
    margin-bottom: 2rem;
  }

  .mobile-test-info {
    margin-top: 2rem;
    padding: 1rem;

    h2 {
      font-size: 1.1rem;
    }
  }

  .info-item {
    padding: 0.8rem;

    h3 {
      font-size: 0.85rem;
    }

    p {
      font-size: 0.8rem;
    }
  }
}
</style>
