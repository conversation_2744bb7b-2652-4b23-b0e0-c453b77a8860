<template>
  <div class="mobile-test-container">
    <h1>📱 移动端图片网格测试</h1>
    
    <!-- 设备尺寸模拟器 -->
    <div class="device-simulator">
      <h2>设备尺寸模拟</h2>
      <div class="device-buttons">
        <button 
          v-for="device in devices" 
          :key="device.name"
          @click="setDeviceWidth(device.width)"
          :class="{ active: currentWidth === device.width }"
          class="device-btn"
        >
          {{ device.name }} ({{ device.width }}px)
        </button>
      </div>
      
      <div class="current-info">
        当前模拟宽度: {{ currentWidth }}px | 
        可用图片宽度: {{ availableWidth }}px |
        布局策略: {{ layoutStrategy }}
      </div>
    </div>

    <!-- 测试区域 -->
    <div class="test-viewport" :style="{ width: currentWidth + 'px' }">
      <div class="mock-container">
        <div class="mock-card">
          <div class="mock-header">用户头像和信息</div>
          <div class="mock-content">这是一条测试微博内容...</div>
          
          <!-- 不同图片数量测试 -->
          <div class="test-case" v-for="testCase in testCases" :key="testCase.count">
            <h3>{{ testCase.count }}张图片</h3>
            <ImageGrid :images="testCase.images" :key-prefix="`mobile-${testCase.count}`" />
          </div>
          
          <!-- 转发微博测试 -->
          <div class="mock-retweet">
            <div class="retweet-header">转发微博</div>
            <div class="retweet-content">转发的微博内容...</div>
            <ImageGrid :images="images4" key-prefix="retweet-test" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ImageGrid from '../components/ImageGrid.vue'

export default {
  name: 'MobileImageGridTest',
  components: {
    ImageGrid
  },
  data() {
    return {
      currentWidth: 375,
      devices: [
        { name: 'iPhone SE', width: 375 },
        { name: '小屏安卓', width: 360 },
        { name: 'iPhone 12', width: 390 },
        { name: '超小屏', width: 320 },
        { name: '大屏手机', width: 414 }
      ],
      sampleImages: [
        'https://picsum.photos/300/300?random=1',
        'https://picsum.photos/300/300?random=2',
        'https://picsum.photos/300/300?random=3',
        'https://picsum.photos/300/300?random=4',
        'https://picsum.photos/300/300?random=5',
        'https://picsum.photos/300/300?random=6',
        'https://picsum.photos/300/300?random=7',
        'https://picsum.photos/300/300?random=8',
        'https://picsum.photos/300/300?random=9'
      ]
    }
  },
  computed: {
    // 计算可用于图片显示的宽度
    availableWidth() {
      // 容器padding: 8px * 2 = 16px
      // 卡片padding: 16px * 2 = 32px  
      // 图片网格padding: 12px * 2 = 24px (移动端优化后)
      return this.currentWidth - 16 - 32 - 24
    },
    
    layoutStrategy() {
      if (this.currentWidth < 360) return '超小屏：5+图片单列'
      if (this.currentWidth < 375) return '小屏：2列布局'
      return '标准：3列布局(5+图片)'
    },
    
    testCases() {
      return [
        { count: 1, images: this.sampleImages.slice(0, 1) },
        { count: 2, images: this.sampleImages.slice(0, 2) },
        { count: 3, images: this.sampleImages.slice(0, 3) },
        { count: 4, images: this.sampleImages.slice(0, 4) },
        { count: 5, images: this.sampleImages.slice(0, 5) },
        { count: 6, images: this.sampleImages.slice(0, 6) },
        { count: 9, images: this.sampleImages.slice(0, 9) }
      ]
    },
    
    images4() {
      return this.sampleImages.slice(0, 4)
    }
  },
  methods: {
    setDeviceWidth(width) {
      this.currentWidth = width
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-test-container {
  padding: 1rem;
  background: #f5f5f5;
  min-height: 100vh;
  
  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 2rem;
  }
}

.device-simulator {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  h2 {
    margin-bottom: 1rem;
    color: #555;
  }
}

.device-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.device-btn {
  padding: 0.5rem 1rem;
  border: 2px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.85rem;
  
  &:hover {
    border-color: #007bff;
    background: #f8f9fa;
  }
  
  &.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
  }
}

.current-info {
  padding: 0.75rem;
  background: #e3f2fd;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #1565c0;
  font-weight: 500;
}

.test-viewport {
  margin: 0 auto;
  background: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  border-radius: 12px;
  overflow: hidden;
  transition: width 0.3s ease;
}

.mock-container {
  padding: 8px; // 模拟 WeiboDetail 的 container padding
}

.mock-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}

.mock-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-weight: 500;
  color: #666;
}

.mock-content {
  padding: 16px;
  color: #333;
}

.test-case {
  border-top: 1px solid #f0f0f0;
  
  h3 {
    padding: 12px 16px 0;
    margin: 0;
    font-size: 0.9rem;
    color: #666;
  }
}

.mock-retweet {
  margin: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  
  .retweet-header {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 8px;
  }
  
  .retweet-content {
    font-size: 0.9rem;
    color: #333;
    margin-bottom: 8px;
  }
}

@media (max-width: 768px) {
  .mobile-test-container {
    padding: 0.5rem;
  }
  
  .device-simulator {
    padding: 1rem;
  }
  
  .device-buttons {
    justify-content: center;
  }
  
  .device-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }
  
  .current-info {
    font-size: 0.8rem;
    text-align: center;
  }
}
</style>
