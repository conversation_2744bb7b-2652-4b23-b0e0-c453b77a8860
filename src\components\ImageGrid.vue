<template>
  <div
    class="image-grid"
    :class="[gridClass, mobileGridClass]"
    v-if="images.length"
  >
    <el-image 
      v-for="(url, index) in images" 
      :key="`${keyPrefix}-${index}`" 
      :src="url" 
      fit="cover" 
      lazy
      :preview-src-list="images"
      :initial-index="index"
      preview-teleported
    >
      <template #error>
        <div class="image-slot">
          <el-icon><Picture /></el-icon>
        </div>
      </template>
    </el-image>
  </div>
</template>

<script>
import { computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

export default {
  name: 'ImageGrid',
  components: {
    Picture
  },
  props: {
    images: {
      type: Array,
      default: () => []
    },
    keyPrefix: {
      type: String,
      default: 'image'
    }
  },
  setup(props) {
    const gridClass = computed(() => {
      const count = props.images.length

      if (count === 0) return ''
      if (count === 1) return 'single-image'
      if (count <= 4) return 'grid-2x2'
      return 'grid-3-cols'
    })

    const mobileGridClass = computed(() => {
      const count = props.images.length

      if (count === 0) return ''
      if (count === 1) return 'mobile-single'
      if (count === 2) return 'mobile-2-cols'
      if (count === 3) return 'mobile-3-special'
      if (count === 4) return 'mobile-2x2'
      return 'mobile-adaptive'
    })

    return {
      gridClass,
      mobileGridClass
    }
  }
}
</script>

<style lang="scss" scoped>
.image-grid {
  display: grid;
  gap: $space-sm;
  padding: 0 $space-xl $space-xl;
  
  @include mobile {
    padding: 0 $space-lg $space-lg;
    gap: $space-xs;
  }
  
  &.single-image {
    grid-template-columns: 1fr;
    max-width: 400px;

    @include mobile {
      max-width: 100%;
    }

    .el-image {
      aspect-ratio: auto;
      max-height: 300px;

      @include mobile {
        max-height: 250px;
      }
    }
  }

  // 1-4张图片：2x2矩形网格布局
  &.grid-2x2 {
    grid-template-columns: repeat(2, 1fr);
    max-width: 600px;

    @include mobile {
      max-width: 100%;
    }
  }

  // 5张及以上图片：每行3列布局
  &.grid-3-cols {
    grid-template-columns: repeat(3, 1fr);
  }

  // 移动端专用布局类
  @include mobile {
    // 移动端单图 - 减少padding节省空间
    &.mobile-single {
      padding: 0 $space-md $space-md;
      max-width: 300px;
    }

    // 移动端2张图片 - 1行2列，紧凑布局
    &.mobile-2-cols {
      grid-template-columns: repeat(2, 1fr);
      padding: 0 $space-md $space-md;
      gap: $space-xs;
    }

    // 移动端3张图片 - 特殊布局：上1下2
    &.mobile-3-special {
      grid-template-columns: repeat(2, 1fr);
      padding: 0 $space-md $space-md;
      gap: $space-xs;

      .el-image:first-child {
        grid-column: 1 / -1;
        aspect-ratio: 2 / 1;
        max-height: 120px;
      }
    }

    // 移动端4张图片 - 标准2x2
    &.mobile-2x2 {
      grid-template-columns: repeat(2, 1fr);
      padding: 0 $space-md $space-md;
      gap: $space-xs;
    }

    // 移动端5张及以上 - 自适应布局
    &.mobile-adaptive {
      padding: 0 $space-md $space-md;
      gap: $space-xs;

      // 根据屏幕宽度动态调整
      grid-template-columns: repeat(2, 1fr);

      // 超小屏幕（<360px）使用单列
      @media (max-width: 359px) {
        grid-template-columns: 1fr;
        max-width: 250px;
      }

      // 中等移动屏幕（>=375px）可以尝试3列
      @media (min-width: 375px) {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }
}

// 图片样式优化
:deep(.el-image) {
  width: 100%;
  aspect-ratio: 1 / 1;
  border-radius: $radius-md;
  background: $bg-tertiary;
  object-fit: cover;
  cursor: pointer;
  transition: all $transition-base;
  overflow: hidden;
  position: relative;

  // 移动端图片样式优化
  @include mobile {
    border-radius: $radius-sm;
    min-height: 80px; // 确保最小高度，避免过度压缩

    // 在小屏幕上减少圆角和阴影以节省视觉空间
    &:hover {
      transform: scale(1.01); // 减少缩放效果
    }
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    transition: background $transition-base;
    z-index: 1;
    pointer-events: none;
  }
  
  &:hover {
    transform: scale(1.02);
    box-shadow: $shadow-3;
    
    &::before {
      background: rgba(0, 0, 0, 0.1);
    }
    
    img {
      transform: scale(1.05);
    }
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  img {
    transition: transform $transition-slow;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 图片加载失败样式
.image-slot {
  @include flex-center;
  width: 100%;
  height: 100%;
  background: $bg-tertiary;
  color: $text-tertiary;
  font-size: 24px;
  border-radius: $radius-md;
  transition: all $transition-base;
  
  @include mobile {
    font-size: 20px;
  }
  
  &:hover {
    background: rgba($bg-tertiary, 0.8);
    color: $text-secondary;
  }
}

// 图片预览优化
:deep(.el-image-viewer__wrapper) {
  z-index: $z-index-modal !important;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

:deep(.el-image-viewer__mask) {
  background: $bg-overlay !important;
  transition: all $transition-base !important;
}

:deep(.el-image-viewer__actions) {
  background: rgba(0, 0, 0, 0.8) !important;
  border-radius: $radius-lg !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: $shadow-3;
  
  .el-image-viewer__actions__inner {
    color: white !important;
    transition: all $transition-fast !important;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
      border-radius: $radius-sm !important;
    }
  }
}

:deep(.el-image-viewer__canvas) {
  transition: all $transition-base !important;
}

// 移动端图片预览优化
@include mobile {
  :deep(.el-image-viewer__wrapper) {
    z-index: $z-index-modal !important;
  }
  
  :deep(.el-image-viewer__canvas) {
    padding: $space-xl !important;
  }
  
  :deep(.el-image-viewer__actions) {
    bottom: $space-xl !important;
  }
}

// 暗色模式适配
@include dark-mode {
  :deep(.el-image) {
    background: $dark-bg-tertiary;
  }
  
  .image-slot {
    background: $dark-bg-tertiary;
    color: $dark-text-tertiary;
    
    &:hover {
      background: rgba($dark-bg-tertiary, 0.8);
      color: $dark-text-secondary;
    }
  }
}

// 高对比度模式
@include high-contrast {
  :deep(.el-image) {
    border: 2px solid currentColor;
  }
  
  .image-slot {
    border: 2px solid currentColor;
  }
}

// 减少动画模式
@include reduced-motion {
  :deep(.el-image) {
    transition: none !important;
    
    &:hover {
      transform: none !important;
      
      img {
        transform: none !important;
      }
    }
    
    img {
      transition: none !important;
    }
  }
}
</style>